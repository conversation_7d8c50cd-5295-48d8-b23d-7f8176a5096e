import 'package:amor_app/common/utils/common_util/common_util.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/ai_image.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/ai_video.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AiPhotoPage extends StatefulWidget {
  const AiPhotoPage({super.key});

  @override
  State<AiPhotoPage> createState() => _AiPhotoPageState();
}

class _AiPhotoPageState extends State<AiPhotoPage> {
  int selectPage = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        spacing: 12,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Row(
              spacing: 8,
              children: [
                pageTitleWidget(
                  title: 'ai_photo'.tr,
                  selected: selectPage == 0,
                  onTap: () => changePage(0),
                ),
                pageTitleWidget(
                  title: 'image_to_video'.tr,
                  selected: selectPage == 1,
                  onTap: () => changePage(1),
                ),
              ],
            ),
          ),
          Expanded(
            child: IndexedStack(
              index: selectPage,
              children: [
                AiImage(),
                AiVideo(),
              ],
            ),
          )
        ],
      ),
    );
  }

  void changePage(int index) {
    setState(() {
      selectPage = index;
    });
  }

  Widget pageTitleWidget({
    required String title,
    required bool selected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: CommonUtil.colorsUtil('#4E4E4E'),
            gradient: selected == true
                ? LinearGradient(
                    colors: [
                      CommonUtil.colorsUtil('#C69351'),
                      CommonUtil.colorsUtil('#EAC282'),
                    ],
                    begin: Alignment.bottomLeft,
                    end: Alignment.topRight,
                  )
                : null),
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 5),
        child: Text(
          title,
          style: TextStyle(fontSize: 12, fontWeight: selected == true ? FontWeight.w600 : FontWeight.w400),
        ),
      ),
    );
  }
}
