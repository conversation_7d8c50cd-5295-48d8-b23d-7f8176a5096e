import 'dart:convert';

class AiStyles {
  final String? name;
  final String? style;
  final String? url;
  final String? price;
  final String? icon;

  AiStyles({
    this.name,
    this.style,
    this.url,
    this.price,
    this.icon,
  });

  factory AiStyles.fromRawJson(String str) => AiStyles.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AiStyles.fromJson(Map<String, dynamic> json) => AiStyles(
        name: json["name"],
        style: json["style"],
        url: json["url"],
        price: json["price"],
        icon: json["icon"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "style": style,
        "url": url,
        "price": price,
        "icon": icon,
      };
}
