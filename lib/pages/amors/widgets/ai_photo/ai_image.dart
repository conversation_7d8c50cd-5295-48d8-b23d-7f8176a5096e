import 'package:amor_app/pages/amors/widgets/ai_photo/ai_step_1.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/ai_step_2.dart';
import 'package:flutter/material.dart';

class AiImage extends StatefulWidget {
  const AiImage({super.key});

  @override
  State<AiImage> createState() => _AiImageState();
}

class _AiImageState extends State<AiImage> {
  int step = 0;
  String imagePath = '';

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: _body(),
        ),
      ],
    );
  }

  Widget _body() {
    if (step == 0) {
      return AiStep1(
        role: null,
        isVideo: false,
        onTapGenRole: () {},
        onTapUpload: (path) {
          setState(() {
            imagePath = path;
            step = 1;
          });
        },
      );
    }
    if (step == 1) {
      return AiStep2(
        onTapGen: () {},
        onDeleteImage: () {},
        role: null,
        isVideo: false,
        onInputTextFinish: (String text) {},
        styles: [],
        onChooseStyles: (styles) {},
        imagePath: '',
        undressRole: false,
        selectedStyel: null,
      );
    }
    return Container();
  }
}
