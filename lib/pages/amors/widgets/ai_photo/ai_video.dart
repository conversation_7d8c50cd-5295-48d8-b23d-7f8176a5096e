import 'package:amor_app/pages/amors/widgets/ai_photo/ai_step_1.dart';
import 'package:flutter/material.dart';

class AiVideo extends StatefulWidget {
  const AiVideo({super.key});

  @override
  State<AiVideo> createState() => _AiVideoState();
}

class _AiVideoState extends State<AiVideo> {
  @override
  Widget build(BuildContext context) {
    return AiStep1(
      role: null,
      isVideo: true,
      onTapGenRole: () {},
      onTapUpload: (path) {},
    );
  }
}
