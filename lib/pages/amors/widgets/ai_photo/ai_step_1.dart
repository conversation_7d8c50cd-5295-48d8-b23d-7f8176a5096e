import 'dart:convert';
import 'dart:io';

import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/loading/loading.dart';
import 'package:amor_app/pages/session/sub_pages/session_set_bg/widgets/permissions_dialog.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:video_player/video_player.dart';

class AiStep1 extends StatefulWidget {
  const AiStep1({
    super.key,
    this.role,
    required this.isVideo,
    this.hasHistory,
    this.onTapGenRole,
    required this.onTapUpload,
  });

  final AmorsFeedModel? role;
  final bool isVideo;
  final bool? hasHistory;
  final VoidCallback? onTapGenRole;
  final void Function(String path) onTapUpload;

  @override
  State<AiStep1> createState() => _AiStep1State();
}

class _AiStep1State extends State<AiStep1> {
  VideoPlayerController? _controller;

  String? _localVideoPath;

  @override
  void initState() {
    super.initState();

    String videoUrl = ObfuscatedAssets.videoUrl;

    ApiRequest.downloadVideo(videoUrl).then((localPath) {
      if (localPath != null) {
        _localVideoPath = localPath;
        if (widget.isVideo) {
          _initVideoPlayer();
        }
      }
    });

    if (widget.isVideo) {
      if (_localVideoPath != null) {
        _initVideoPlayer();
      }
    }
  }

  void _initVideoPlayer() {
    _controller = VideoPlayerController.file(File(_localVideoPath!));
    _controller?.initialize().then((_) {
      _controller?.setLooping(true);
      _controller?.play();
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    final text = !widget.isVideo ? 'upload_steps_extra'.tr : 'upload_steps'.tr;
    final text2 = !widget.isVideo ? 'undress_sweetheart'.tr : 'make_photo_animated'.tr;

    final imgW = MediaQuery.sizeOf(context).width - 24;
    final imgH = imgW / 3 * 4;

    bool hasRole = widget.role != null;

    String imageUrl = ObfuscatedAssets.imageUrl;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Stack(alignment: Alignment.bottomCenter, children: [
        SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              Center(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  clipBehavior: Clip.hardEdge,
                  child: Container(
                    color: const Color(0xFF232321),
                    height: imgH,
                    width: imgW,
                    child: widget.isVideo
                        ? (_controller?.value.isInitialized ?? false)
                            ? AspectRatio(
                                aspectRatio: _controller!.value.aspectRatio,
                                child: VideoPlayer(_controller!),
                              )
                            : Container()
                        : Container(
                            color: Colors.amber,
                            child: CachedNetworkImage(
                              imageUrl: imageUrl,
                              fit: BoxFit.cover,
                            ),
                          ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                text,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.7),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 8),
              Center(
                child: Text(
                  text2,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SizedBox(height: 80),
            ],
          ),
        ),
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
              onTap: selecImage,
              child: Container(
                height: 44,
                width: 240,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(22),
                  color: CommonUtil.colorsUtil('#4E4E4E'),
                  gradient: LinearGradient(
                    colors: [
                      CommonUtil.colorsUtil('#C69351'),
                      CommonUtil.colorsUtil('#EAC282'),
                    ],
                    begin: Alignment.bottomLeft,
                    end: Alignment.topRight,
                  ),
                ),
                child: Center(
                  child: Text(
                    'Upload a photo'.tr,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ),
            if (hasRole) ...[
              const SizedBox(height: 8),
              GestureDetector(
                onTap: widget.onTapGenRole,
                child: Container(
                  height: 48,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border.all(width: 1, color: Colors.white),
                    borderRadius: BorderRadius.circular(24),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    widget.hasHistory == true ? 'view_nude'.tr : 'undr_role'.tr,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
            const SizedBox(height: 10),
          ],
        ),
      ]),
    );
  }

  selecImage() async {
    String? path = await SelectMediaUtil.selectImage(
      ratioX: 1.sw,
      ratioY: 1.sh - 80.w,
      source: ImageSource.gallery,
      noticePermissions: false,
    );

    if (path == 'no permissions') {
      noPermissions();
      return;
    }
    if (path == null) {
      Loading.toast('Please try again later');
      return;
    }

    widget.onTapUpload(path);
  }

  //缺少相册权限
  noPermissions() async {
    int? result = await Get.dialog(const PermissionsDialogWidget());
    if (result == 1) {
      openAppSettings();
    }
  }
}

class ObfuscatedAssets {
  static const _imageEncoded = 'aHR0cHM6Ly9zdGF0aWMuYW1vcmFpLm5ldC8yMDI1LTA2LTExLzIwMjUwNjExLTE4MDk1Ni5qcGVn';
  static const _videoEncoded = 'aHR0cHM6Ly9zdGF0aWMuYW1vcmFpLm5ldC9pbWFnZXMvMTkyNjg4Nzk0NTA3ODM1ODAxOC5tcDQ=';

  static String _decode(String base64Str) => utf8.decode(base64.decode(base64Str));

  static String get imageUrl => _decode(_imageEncoded);
  static String get videoUrl => _decode(_videoEncoded);
}
