import 'package:amor_app/common/models/amors_models/amors_category_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/ai_photo_page.dart';
import 'package:amor_app/pages/amors/widgets/explore_list.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lazy_load_indexed_stack/lazy_load_indexed_stack.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import 'controller.dart';
import 'widgets/clothing_list/widget.dart';
import 'widgets/create/widget.dart';
import 'widgets/moments_list/widget.dart';
import 'widgets/video_list/widget.dart';

class AmorsPage extends StatelessWidget {
  const AmorsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.mainBg,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: false,
        title: titleMenu(),
        actions: [
          Obx(() => AmorsPageController.to.state.showSearch.value == true
              ? Padding(
                  padding: const EdgeInsets.only(right: 20),
                  child: ImageBtn(
                      iconSting: Assets.assetsImagesSearchWhite,
                      onPressed: () => AmorsPageController.to.showSearchWidget(),
                      width: 22,
                      height: 22),
                )
              : Container()),
          if (AmorTraService.ar) 10.horizontalSpace,
        ],
      ),
      body: GetBuilder<AmorsPageController>(
        init: AmorsPageController(),
        builder: (controller) {
          return Column(
            children: [
              Obx(() =>
                  AmorsPageController.to.state.showSearch.value == true && controller.state.topMenuIndex.value == 0
                      ? tagsMenuWidget(controller)
                      : Container()),
              Expanded(
                child: Obx(
                  () => LazyLoadIndexedStack(
                    index: controller.state.topMenuIndex.value,
                    children: [
                      ExploreListWidget(),
                      HomeClothListWidget(),
                      HomeVideoListWidget(),
                      HomeMomentsListWidget(),
                      HomeCreatWidget(),
                      AiPhotoPage(),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  //tag菜单
  Widget tagsMenuWidget(AmorsPageController controller) {
    return controller.state.tagsList.length <= 1
        ? const SizedBox()
        : Container(
            // color: Colors.amber,
            width: double.infinity,
            margin: EdgeInsets.only(bottom: 10.w),
            height: 36.w,
            child: Stack(
              children: [
                NotificationListener<OverscrollIndicatorNotification>(
                  onNotification: (OverscrollIndicatorNotification? overscroll) {
                    overscroll!.disallowIndicator();
                    return true;
                  },
                  child: Obx(
                    () => controller.state.tagsList.isEmpty
                        ? Container()
                        : ScrollablePositionedList.builder(
                            physics: const ClampingScrollPhysics(),
                            itemScrollController: controller.tagScrollCtl,
                            padding: AmorTraService.ar ? EdgeInsets.zero : EdgeInsets.only(left: 8.w, right: 20.w),
                            scrollDirection: Axis.horizontal,
                            itemCount: controller.state.tagsList.length,
                            itemBuilder: (BuildContext context, int index) {
                              AmorsCategoryModel tagModel = controller.state.tagsList.elementAt(index);
                              return InkWell(
                                onTap: () {
                                  if (controller.state.selectedCategoryIndex.value != index) {
                                    controller.state.selectedCategoryIndex.value = index;
                                    controller.state.clearList = true;
                                    controller.state.dataList.clear();
                                    controller.selectedCategory(index);
                                    ReportUtil.reportEvents(
                                        page: ReportUtil.amor, action: ReportUtil.select, value: tagModel.cateName);
                                  }
                                },
                                child: Obx(
                                  () => Container(
                                    constraints: tagModel.label != null ? BoxConstraints(minWidth: 60.w) : null,
                                    child: Stack(
                                      children: [
                                        Align(
                                          alignment: Alignment.bottomCenter,
                                          child: Container(
                                            height: 30.w,
                                            padding: EdgeInsets.symmetric(horizontal: 10.w),
                                            margin: EdgeInsets.fromLTRB(4.w, 6.w, 4.w, 0),
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(30.w / 2),
                                              border: Border.all(
                                                color: controller.state.selectedCategoryIndex.value == index
                                                    ? AppColor.colorsUtil('#F0BE72')
                                                    : Colors.white.withValues(alpha: 0.5),
                                              ),
                                            ),
                                            child: Row(
                                              children: [
                                                Text(
                                                  tagModel.cateName ?? '',
                                                  style: TextStyle(
                                                    fontSize: 14.sp,
                                                    color: controller.state.selectedCategoryIndex.value == index
                                                        ? AppColor.colorsUtil('#F0BE72')
                                                        : Colors.white.withValues(alpha: 0.5),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        if (tagModel.label != null)
                                          Positioned(
                                            left: 7.w,
                                            top: 0,
                                            child: Image.asset(
                                              tagModel.label == 'nsfw'
                                                  ? Assets.assetsImagesTagNsfw
                                                  : Assets.assetsImagesTagBdsm,
                                              width: 48.w,
                                            ),
                                          )
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                  ),
                ),
                Align(
                  alignment: AmorTraService.ar ? Alignment.centerLeft : Alignment.centerRight,
                  child: ImageBtn(
                      iconSting: AmorTraService.ar ? Assets.assetsImagesHomeShowTagAr : Assets.assetsImagesHomeShowTag,
                      onPressed: () {
                        controller.showTagView();
                      },
                      width: 36.w,
                      height: 36.w),
                ),
              ],
            ),
          );
  }

  //顶部菜单
  Widget titleMenu() {
    return SizedBox(
      // color: Colors.amber,
      width: 1.sw - 50,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Obx(
          () => Row(
            children: [
              titleMenuItem(
                  title: 'Explore'.tr,
                  showBadge: false,
                  selected: AmorsPageController.to.state.topMenuIndex.value == 0,
                  index: 0),
              if (AppService.audit == false)
                titleMenuItem(
                    title: 'Outfit'.tr,
                    showBadge: false,
                    selected: AmorsPageController.to.state.topMenuIndex.value == 1,
                    index: 1),
              if (AppService.audit == false)
                titleMenuItem(
                    title: 'Video'.tr,
                    showBadge: false,
                    icon: Assets.assetsImagesHomeVideoTitleSelect,
                    selectedIcon: Assets.assetsImagesHomeVideoTitleSelected,
                    selected: AmorsPageController.to.state.topMenuIndex.value == 2,
                    index: 2),
              titleMenuItem(
                  title: 'Moments'.tr,
                  showBadge: AppService.sp.get(spHomeMomentsOntap) == null,
                  selected: AmorsPageController.to.state.topMenuIndex.value == 3,
                  index: 3),
              if (AppService.audit == false)
                titleMenuItem(
                    title: 'Generate'.tr,
                    showBadge: AppService.sp.get(spHomeCreateOntap) == null,
                    selected: AmorsPageController.to.state.topMenuIndex.value == 4,
                    index: 4),
              if (AppService.audit == false)
                titleMenuItem(
                    title: 'ai_photo'.tr,
                    showBadge: false,
                    selected: AmorsPageController.to.state.topMenuIndex.value == 5,
                    index: 5),
            ],
          ),
        ),
      ),
    );
  }

  Widget titleMenuItem({
    required String title,
    required bool showBadge,
    required bool selected,
    String? icon,
    String? selectedIcon,
    required int index,
  }) {
    return InkWell(
      onTap: () => AmorsPageController.to.onTapTitle(index),
      child: Container(
        margin: AmorTraService.ar
            ? EdgeInsets.only(left: title != 'ai_photo'.tr ? 16.w : 0)
            : EdgeInsets.only(right: title != 'ai_photo'.tr ? 16.w : 0),
        height: 28.w,
        // width: 50,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: CommonUtil.gradientText(
                text: title,
                colors: selected
                    ? [AppColor.colorsUtil('#FFDCA4'), AppColor.colorsUtil('#C8984A')]
                    : [CommonUtil.colorsUtil('#949494'), CommonUtil.colorsUtil('#949494')],
                textStyle: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: selected ? FontWeight.w800 : FontWeight.w500,
                    fontStyle: FontStyle.italic),
              ),
            ),
            if (icon != null && selectedIcon != null)
              Positioned(
                right: 0,
                top: 0,
                child: Image.asset(
                  selected ? selectedIcon : icon,
                  height: 12,
                  fit: BoxFit.fitHeight,
                ),
              ),
            if (showBadge == true)
              Positioned(
                right: 1.w,
                top: 8,
                child: Image.asset(
                  Assets.assetsImagesHomeVideoTitleBadge,
                  height: 4,
                  fit: BoxFit.fitHeight,
                ),
              )
          ],
        ),
      ),
    );
  }
}
